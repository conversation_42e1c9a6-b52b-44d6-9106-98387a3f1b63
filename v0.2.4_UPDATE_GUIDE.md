# Context Portal MCP Update Guide: v0.1.x to v0.2.4+

This guide provides comprehensive instructions for updating your `context-portal-mcp` installation from version `0.1.9` or earlier to `v0.2.4` and beyond. This update includes significant changes, notably the integration of **Alembic** for database migrations and a new `timestamp` field for `CustomData` entries, along with crucial bug fixes.

To ensure a smooth transition and data integrity, please follow these steps carefully.

## TLDR: Quick Update Steps

For a detailed explanation of each step, please refer to the sections below.

1.  **Pre-Upgrade Cleanup:** Clear `__pycache__` and `.pyc` files.
2.  **Determine Your Upgrade Path:**
    *   **From `v0.1.9`:** Attempt Automatic Migration (Section 4).
    *   **From `v0.1.8` or Earlier:** Perform Manual Data Migration (Section 5).
3.  **Automatic Migration (if applicable):**
    *   Stop ConPort processes.
    *   Uninstall old `context-portal-mcp` package.
    *   Install new `context-portal-mcp` package (`uv pip install context-portal-mcp`).
    *   Restart ConPort server.
    *   Verify logs.
4.  **Manual Data Migration (if applicable):**
    *   Stop ConPort processes.
    *   Export existing data (ask AI assistant to export).
    *   Uninstall old `context-portal-mcp` package.
    *   Clean up lingering package files (optional).
    *   Delete old `context.db` file.
    *   Install new `context-portal-mcp` package (`uv pip install context-portal-mcp`).
    *   **Crucial:** Copy `alembic.ini` from `site-packages` to your project workspace root (for existing workspaces; new workspaces provision this automatically).
    *   Restart ConPort server.
    *   Import data back (ask AI assistant to import).

## Key Improvements in v0.2.4+

This release addresses critical issues encountered in previous versions, simplifying the update process and improving reliability:

*   **Alembic Configuration Bundled & Placed:** The `alembic/` directory and `alembic.ini` are now correctly bundled within the `context-portal-mcp` package. **When starting ConPort in a new workspace (where `context_portal/` and `context.db` do not yet exist), these files will be automatically provisioned in your workspace root.** For Alembic to function, `alembic.ini` **must be present in your project's workspace root directory** (the directory containing your `context_portal` folder and `context.db`). Regardless of where you installed `context-portal-mcp` (e.g., in a central virtual environment or directly in your workspace), you may need to manually copy this file from your Python `site-packages` after installation (see Section 5, Step 6.5). This resolves previous issues where users had to manually initialize Alembic or configure `script_location`, making database migrations more robust and automatic for supported upgrade paths.
*   **Robust UTF-8 Export:** ConPort's data export functionality now explicitly handles UTF-8 encoding during file writing, resolving `'charmap' codec can't encode characters` errors, especially on Windows. Your exported data will now reliably preserve all characters.

## 1. Pre-Upgrade Cleanup

Before proceeding with any database operations or package updates, it's crucial to clear any old compiled Python files (`.pyc`) and `__pycache__` directories. This prevents potential conflicts with the new code.

*   **Locate and Delete `__pycache__` and `.pyc` files:**
    *   **On Windows (Command Prompt/PowerShell):**
        ```cmd
        for /d /r . %d in (__pycache__) do @rd /s /q "%d"
        del /s /q *.pyc
        ```
    *   **On Linux/macOS (Bash/Zsh):**
        ```bash
        find . -name "__pycache__" -type d -exec rm -rf {} +
        find . -name "*.pyc" -delete
        ```
    (Run these commands from your `context-portal` workspace root directory.)

## 2. Understanding Database Migrations

Starting with `v0.1.9`, ConPort utilizes **Alembic** for automated database schema management. This means:

*   **Seamless Upgrades (for v0.1.9 users):** If you are updating from `v0.1.9`, Alembic will generally handle database schema changes automatically when the ConPort server starts.
*   **Data Preservation:** Alembic migrations are designed to preserve your existing data during schema upgrades.
*   **No Manual Intervention (Typically for v0.1.9 users):** For most updates from `v0.1.9`, simply updating the `context-portal-mcp` package and restarting the ConPort server is all that's required.

However, for users on `v0.1.8` or earlier, or in specific scenarios, a manual migration is necessary.

## 3. Determining Your Upgrade Path

*   **Upgrading from `v0.1.9`:** You *might* be able to rely on automatic migration. Proceed to Section 4. If you encounter any issues, fall back to Section 5 (Manual Data Migration).
*   **Upgrading from `v0.1.8` or Earlier (e.g., `v0.1.6`, `v0.1.7`):** You **must** perform a manual data migration. This is due to significant schema changes (like the addition of the `timestamp` field to `CustomData` and the initial Alembic setup) that cannot be automatically handled by Alembic from these older versions. Proceed directly to Section 5.

### Handling Multiple Workspaces

If you manage multiple project workspaces, each with its own `context_portal/context.db` file, please note the following:

*   **Package Update is Global:** The `context-portal-mcp` package itself is installed into your Python environment (e.g., a virtual environment). This installation is a one-time operation for that environment and serves *all* workspaces configured to use it. You only need to uninstall the old package and install the new one once.
*   **Database Migration is Per-Workspace:** Each `context.db` file is independent. If a specific workspace's `context.db` requires a manual migration (i.e., it's from `v0.1.8` or earlier, or automatic migration failed), you **must** perform the "Manual Data Migration" steps (Section 5) *for that specific workspace*.
    *   **Exporting Data:** When exporting data (Section 5, Step 2), ensure you are in the correct workspace and use a unique `output_path` for each workspace's backup (e.g., `"conport_backup_project_A"`, `"conport_backup_project_B"`).
    *   **Deleting `context.db`:** Delete the `context.db` file (Section 5, Step 5) only for the workspace you are currently migrating.
    *   **Importing Data:** When importing data back (Section 5, Step 8), ensure you are in the correct workspace and use the corresponding `input_path` for that workspace's backup.

This ensures that each workspace's data is correctly migrated without affecting others.

## 4. Automatic Migration (for v0.1.9 users - Optional)

If you are currently on `v0.1.9`, you can attempt an automatic upgrade:

1.  **STOP ALL CONPORT PROCESSES IMMEDIATELY AND COMPLETELY:**
    This is the most critical step. Ensure no `python.exe` processes related to `context_portal_mcp.main` are running. Failure to do so can lead to database locking and corruption. Refer to Step 1 of "Manual Data Migration" (Section 5) for detailed instructions on how to identify and terminate processes.

2.  **Uninstall the Old Package (Strongly Recommended for Cleanliness):**
    Using `uv uninstall` is preferred for a cleaner removal.
    ```bash
    uv pip uninstall context-portal-mcp
    # Alternatively, using pip:
    # pip uninstall context-portal-mcp
    ```
    (Confirm with `y` if prompted by `uv` or `pip`.)

3.  **Install the New `context-portal-mcp` Package:**
    ```bash
    uv pip install context-portal-mcp
    # Alternatively, using pip:
    # pip install context-portal-mcp
    ```

4.  **Restart the ConPort Server:** Start the ConPort server. Alembic should automatically apply any necessary schema updates.

5.  **Verify:** Check your ConPort logs (`logs/conport.log`) for any database-related errors. If you encounter issues, proceed to Section 5.

## 5. Manual Data Migration (Recommended for v0.1.8 and Earlier, or if Automatic Fails)

This process ensures data integrity by exporting your existing data, rebuilding the database with the latest schema, and then re-importing your data.


**Steps for Manual Data Migration:**

1.  **STOP ALL CONPORT PROCESSES IMMEDIATELY AND COMPLETELY:**
    This is the most critical step. Before proceeding, ensure the ConPort server process is completely stopped. If it's running in a terminal, press `Ctrl+C`. If it's running as a background process (e.g., in STDIO mode without a visible terminal), you **must** identify and terminate its process.
    *   **On Windows (Command Prompt/PowerShell):**
        *   Find Python processes: `wmic process where "name='python.exe'" get CommandLine,ProcessId`
        *   Look for lines containing `context_portal_mcp.main`. Note down their PIDs.
        *   Terminate processes: `taskkill /PID <PID1> /PID <PID2> /F` (replace `<PIDx>` with actual PIDs).
    *   **On Linux/macOS (Bash/Zsh):**
        *   Find process: `ps aux | grep context_portal_mcp.main`
        *   Note down the PID (usually the second column).
        *   Terminate: `kill <PID>` (or `kill -9 <PID>` for a forceful kill if `kill` doesn't work).

2.  **Export Existing Data:**
    Before proceeding, you need to export all your current ConPort data to markdown files. This will create a directory (default: `conport_export/`) containing markdown representations of your Product Context, Active Context, Decisions, Progress, System Patterns, and Custom Data.

    **How to export:**
    Ask your AI assistant (e.g., Roo Code, CLine) to export your ConPort data. You can use a natural language prompt like:
    "Please export my ConPort data to a backup directory named `conport_backup`."

    If your AI assistant requires a specific command, it might look something like this (though your assistant should handle this for you):
    ```xml
    <use_mcp_tool>
    <server_name>conport</server_name>
    <tool_name>export_conport_to_markdown</tool_name>
    <arguments>
    {
      "workspace_id": "${workspaceFolder}",
      "output_path": "conport_backup"
    }
    </arguments>
    </use_mcp_tool>
    ```
    (Ensure `${workspaceFolder}` is correctly resolved by your IDE, or replace it with the absolute path to your project workspace.)

3.  **Uninstall the Old `context-portal-mcp` Package:**
    This step removes the old package files from your Python environment, preventing conflicts. Using `uv uninstall` is preferred for a cleaner removal.
    ```bash
    uv pip uninstall context-portal-mcp
    # Alternatively, using pip:
    # pip uninstall context-portal-mcp
    ```
    (Confirm with `y` if prompted by `uv` or `pip`.)

4.  **Clean Up Lingering Package Files (Optional but Recommended):**
    Sometimes `pip uninstall` might leave behind empty directories or `.dist-info` files. Manually remove them for a pristine state. You'll need to find your Python `site-packages` directory. Common locations include:
    *   **Windows:** `C:\PythonXX\Lib\site-packages` or `C:\Users\<USER>\AppData\Local\Programs\Python\PythonXX\Lib\site-packages`
    *   **Linux/macOS:** `/usr/local/lib/pythonX.Y/dist-packages` or `~/.local/lib/pythonX.Y/site-packages` (for user installs)

    Once you've located your `site-packages` directory, navigate into it and look for directories starting with `context_portal_mcp` or `context_portal_mcp-0.x.x.dist-info`.

    *   **On Windows (Command Prompt/PowerShell - from `site-packages`):**
        ```cmd
        rmdir /s /q context_portal_mcp
        for /d %d in (context_portal_mcp-0.*.dist-info) do @rmdir /s /q "%d"
        ```
    *   **On Linux/macOS (Bash/Zsh - from `site-packages`):**
        ```bash
        rm -rf context_portal_mcp
        rm -rf context_portal_mcp-0.*.dist-info
        ```

5.  **Delete the Old `context.db` File:**
    Once your data is safely exported and the old package is uninstalled, delete the `context.db` file. This file is located in the `context_portal/` subdirectory within your project workspace.

    *   **On Windows (Command Prompt/PowerShell):**
        ```cmd
        del %CD%\context_portal\context.db
        ```
    *   **On Linux/macOS (Bash/Zsh):**
        ```bash
        rm ./context_portal/context.db
        ```
    (These commands assume you are running them from your project workspace root. If not, replace `%CD%` or `./` with the absolute path to your workspace.)

6.  **Install the New `context-portal-mcp` Package:**
    ```bash
    uv pip install context-portal-mcp
    # Alternatively, using pip:
    # pip install context-portal-mcp
    ```

6.5. **Copy `alembic.ini` to Your Project Workspace Root:**
    For Alembic to correctly manage your project's database, the `alembic.ini` file **must be present in the root directory of your specific project workspace** (the directory that contains your `context_portal/` folder and `context.db` file).

    You will need to locate the `alembic.ini` file within your Python environment's `site-packages` directory where `context-portal-mcp` is installed. It is typically found at a path similar to:
    `path/to/your/python_env/Lib/site-packages/context_portal_mcp/templates/alembic/alembic.ini`
    (Replace `path/to/your/python_env` with the absolute path to your virtual environment or Python installation, and `X.Y.Z` with your installed `context-portal-mcp` version, e.g., `0.2.4`).

    Once located, copy this file to your project's workspace root.

    *   **On Windows (Command Prompt/PowerShell):**
        ```cmd
        # Example: Adjust SOURCE_PATH and TARGET_PATH to your actual directories
        copy "C:\Users\<USER>\path\to\your\venv\Lib\site-packages\context_portal_mcp\templates\alembic\alembic.ini" "C:\Users\<USER>\path\to\your\project_workspace_root\"
        ```
        (Replace `SOURCE_PATH` with the full path to `alembic.ini` in `site-packages`, and `TARGET_PATH` with the absolute path to your project's workspace root.)

    *   **On Linux/macOS (Bash/Zsh):**
        ```bash
        # Example: Adjust SOURCE_PATH and TARGET_PATH to your actual directories
        cp /path/to/your/venv/lib/pythonX.Y/site-packages/context_portal_mcp/templates/alembic/alembic.ini /path/to/your/project_workspace_root/
        ```
        (Replace `SOURCE_PATH` with the full path to `alembic.ini` in `site-packages`, and `TARGET_PATH` with the absolute path to your project's workspace root.)

7.  **Restart the ConPort Server:**
    Start the ConPort server again. Since `context.db` no longer exists, Alembic will automatically create a new database with the latest schema defined by the installed `context-portal-mcp` version.

8.  **Import Data Back:**
    Once the new database is created, you can re-import your saved data into it.

    **How to import:**
    Ask your AI assistant to import your ConPort data from the backup directory you created earlier. You can use a natural language prompt like:
    "Please import my ConPort data from the `conport_backup` directory."

    If your AI assistant requires a specific command, it might look something like this (though your assistant should handle this for you):
    ```xml
    <use_mcp_tool>
    <server_name>conport</server_name>
    <tool_name>import_markdown_to_conport</tool_name>
    <arguments>
    {
      "workspace_id": "${workspaceFolder}",
      "input_path": "conport_backup"
    }
    </arguments>
    </use_mcp_tool>
    ```
    (Ensure `input_path` matches the `output_path` you used during export. Also, note that **Custom Data values must be valid JSON** for successful import. If a custom data entry's value is not valid JSON, it will be skipped during import, and a warning will be logged.)
