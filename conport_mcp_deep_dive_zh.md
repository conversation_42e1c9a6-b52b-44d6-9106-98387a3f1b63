# Context Portal (ConPort) MCP 服务器文档

版本：0.1.0

## 1. 介绍与概述

### 目的
Context Portal (ConPort) MCP 服务器旨在为 AI 助手管理和提供结构化的项目上下文，特别是在集成开发环境 (IDE) 中。其主要目标是通过维护一个可查询、持久化且特定于工作空间的**知识图谱**来增强 AI 对特定软件项目的上下文理解。这个结构化的知识库旨在作为**检索增强生成 (RAG)** 的强大后端，使 AI 助手能够访问精确、最新的信息。这种方法为更简单的基于文件的上下文系统提供了结构化的替代方案。

### 核心技术
*   **语言/框架：** Python，利用 FastAPI 框架提供强大的 Web 服务器功能，使用 Pydantic 进行数据验证和建模。
*   **MCP 集成：** 利用 `mcp.py` SDK 中的 `FastMCP` 库将其功能作为模型上下文协议 (MCP) 工具公开。
*   **数据库：** 使用 SQLite 作为后端数据库，为每个不同的工作空间创建和管理单独的数据库文件。这确保了项目之间的数据隔离。

### 关键架构概念
*   **工作空间特定上下文：** ConPort 管理的所有数据都与 `workspace_id`（通常是项目目录的绝对路径）绑定。这确保上下文与正在处理的特定项目保持相关。
*   **双重通信模式：** 服务器可以在两种模式下运行：
    *   **STDIO（标准输入/输出）：** 用于与 MCP 客户端（例如，像 Roo Code 这样的 IDE 扩展）的直接本地通信。此模式对于本地进程间通信是高效的。
    *   **HTTP（超文本传输协议）：** 服务器也可以作为 HTTP 服务运行（使用 Uvicorn 和 FastAPI），通过 HTTP 端点（通常是 `/mcp`）公开其 MCP 工具。这允许需要或偏好 HTTP 通信的客户端更广泛地访问。
*   **结构化数据管理：** ConPort 定义了几个核心数据实体（见第 2 节）来构建项目知识，如决策、进度、系统模式和自定义数据。
*   **基于工具的交互：** AI 助手通过调用其定义的 MCP 工具与 ConPort 交互，每个工具都为特定操作而设计（例如，记录决策、检索活动上下文）。
*   **知识图谱构建：** ConPort 通过存储结构化实体（决策、代码模式、术语表条目）并允许使用 `link_conport_items` 等工具在它们之间定义显式、可查询的关系来促进项目特定知识图谱的创建。
*   **向量嵌入和语义搜索：** ConPort 为各种实体中的关键文本内容生成和存储向量嵌入，实现语义相似性搜索。
*   **RAG 启用：** 该系统旨在成为检索增强生成 (RAG) 工作流的核心组件。其丰富的查询功能（FTS、语义搜索、直接检索、图遍历）允许 AI 代理获取相关上下文以增强其生成任务，从而产生更准确和有根据的输出。

## 2. 核心数据实体和数据库架构

ConPort 使用特定于每个工作空间的 SQLite 数据库来存储其结构化上下文。关键数据实体及其对应的数据库表包括：

1.  **产品上下文 (`product_context`)**
    *   **目的：** 存储关于项目的高级、相对静态的信息（例如，总体目标、架构、关键特性）。
    *   **架构：** 单行表。
        *   `id` (INTEGER, PK, 固定为 1)
        *   `content` (TEXT): 存储表示产品上下文信息字典的 JSON 字符串。
    *   **历史：** 更改在 `product_context_history` 中进行版本控制。

2.  **活动上下文 (`active_context`)**
    *   **目的：** 存储与当前任务或会话相关的动态、短期上下文（例如，当前焦点、最近更改、开放问题、下一步）。
    *   **架构：** 单行表。
        *   `id` (INTEGER, PK, 固定为 1)
        *   `content` (TEXT): 存储表示活动上下文信息字典的 JSON 字符串。
    *   **历史：** 更改在 `active_context_history` 中进行版本控制。

3.  **决策 (`decisions`)**
    *   **目的：** 记录项目期间做出的重要架构或实现决策。
    *   **架构：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `timestamp` (TIMESTAMP): 决策记录的时间。
        *   `summary` (TEXT, NOT NULL): 决策的简洁摘要。
        *   `rationale` (TEXT): 决策背后的推理。
        *   `implementation_details` (TEXT): 决策将如何/已经实现。
        *   `tags` (TEXT): 用于分类的标签的 JSON 字符串化列表。
    *   **搜索：** 由 FTS5 虚拟表 `decisions_fts` 支持（搜索摘要、推理、实现细节、标签）。

4.  **进度条目 (`progress_entries`)**
    *   **目的：** 跟踪任务、其状态和层次结构。
    *   **架构：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `timestamp` (TIMESTAMP): 进度条目记录/更新的时间。
        *   `status` (TEXT, NOT NULL): 例如，"TODO"、"IN_PROGRESS"、"DONE"。
        *   `description` (TEXT, NOT NULL): 任务或进度的描述。
        *   `parent_id` (INTEGER, FK to `progress_entries.id`): 用于子任务。

5.  **系统模式 (`system_patterns`)**
    *   **目的：** 记录在项目中观察到或使用的重复架构或设计模式。
    *   **架构：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `name` (TEXT, UNIQUE, NOT NULL): 模式的唯一名称。
        *   `description` (TEXT): 模式的描述。
        *   `tags` (TEXT): 标签的 JSON 字符串化列表。

6.  **自定义数据 (`custom_data`)**
    *   **目的：** 存储用户分类的任意键值数据。对项目术语表、配置片段、笔记等有用。
    *   **架构：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `category` (TEXT, NOT NULL)
        *   `key` (TEXT, NOT NULL): 在其类别内唯一。
        *   `value` (TEXT): 存储表示任意数据的 JSON 字符串。
    *   **搜索：** 由 FTS5 虚拟表 `custom_data_fts` 支持（搜索类别、键和值的文本内容）。

7.  **上下文链接 (`context_links`)**
    *   **目的：** 在不同的 ConPort 数据实体之间建立显式、可查询的关系（例如，决策由系统模式实现，进度项跟踪决策），形成**项目知识图谱**的边。
    *   **架构：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `timestamp` (TIMESTAMP): 链接创建的时间。
        *   `workspace_id` (TEXT, NOT NULL): 此链接所属的工作空间。
        *   `source_item_type` (TEXT, NOT NULL): 源项的类型（例如，"decision"）。
        *   `source_item_id` (TEXT, NOT NULL): 源项的 ID/键。
        *   `target_item_type` (TEXT, NOT NULL): 目标项的类型。
        *   `target_item_id` (TEXT, NOT NULL): 目标项的 ID/键。
        *   `relationship_type` (TEXT, NOT NULL): 链接的性质（例如，"implements"、"related_to"）。
        *   `description` (TEXT): 链接本身的可选描述。

8.  **上下文历史 (`product_context_history`, `active_context_history`)**
    *   **目的：** 跟踪产品上下文和活动上下文随时间的变化。
    *   **架构（对于每个历史表）：**
        *   `id` (INTEGER, PK, AUTOINCREMENT)
        *   `timestamp` (TIMESTAMP): 此历史版本保存的时间。
        *   `version` (INTEGER, NOT NULL): 上下文的版本号。
        *   `content` (TEXT, NOT NULL): 此版本上下文的 JSON 字符串内容。
        *   `change_source` (TEXT): 触发更改的简要描述（例如，工具名称）。

9. **向量存储 (ChromaDB)**
    *   **目的：** 存储从各种 ConPort 实体（决策、进度、自定义数据等）的文本内容生成的向量嵌入，以实现语义相似性搜索。
    *   **位置：** 存储在工作空间的 ConPort 数据目录中的磁盘上，与 SQLite 文件分离但与其一起管理。
    *   **集成：** 通过与向量一起存储的项类型和 ID 元数据链接到 SQLite 数据。

在 `src/context_portal_mcp/db/models.py` 中定义的 Pydantic 模型镜像这些表结构，并用于服务器内的数据验证和序列化/反序列化。

## 3. MCP 工具参考

ConPort 服务器公开以下 MCP 工具。这些工具允许 AI 代理与**项目知识图谱**交互并在其基础上构建，并检索对**检索增强生成 (RAG)** 至关重要的特定信息。所有工具都需要一个 `workspace_id` 参数，这是工作空间的标识符（例如，绝对路径），以确保数据操作在正确的上下文中执行。

### 3.1 产品上下文工具

#### 3.1.1 `get_product_context`
*   **描述：** 获取产品或活动上下文的参数（仅需要 workspace_id）。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
*   **Pydantic 模型：** `GetContextArgs`

#### 3.1.2 `update_product_context`
*   **描述：** 更新产品或活动上下文的参数。提供 'content' 进行完整更新或 'patch_content' 进行部分更新。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `content` (object, 可选): 作为字典的完整新上下文内容。覆盖现有内容。（默认：null）
    *   `patch_content` (object, 可选): 要应用的更改字典（添加/更新键）。使用值 `\"__DELETE__\"` 删除键。（默认：null）
*   **Pydantic 模型：** `UpdateContextArgs`

### 3.2 活动上下文工具

#### 3.2.1 `get_active_context`
*   **描述：** 获取产品或活动上下文的参数（仅需要 workspace_id）。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
*   **Pydantic 模型：** `GetContextArgs`

#### 3.2.2 `update_active_context`
*   **描述：** 更新产品或活动上下文的参数。提供 'content' 进行完整更新或 'patch_content' 进行部分更新。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `content` (object, 可选): 作为字典的完整新上下文内容。覆盖现有内容。（默认：null）
    *   `patch_content` (object, 可选): 要应用的更改字典（添加/更新键）。使用值 `\"__DELETE__\"` 删除键。（默认：null）
*   **Pydantic 模型：** `UpdateContextArgs`

### 3.3 决策记录工具

#### 3.3.1 `log_decision`
*   **描述：** 记录决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `summary` (string): 决策的简洁摘要（必需：是）
    *   `rationale` (string, 可选): 决策背后的推理（默认：null）
    *   `implementation_details` (string, 可选): 关于决策将如何/已经实现的详细信息（默认：null）
    *   `tags` (array of strings, 可选): 用于分类的可选标签（默认：null）
*   **Pydantic 模型：** `LogDecisionArgs`

#### 3.3.2 `get_decisions`
*   **描述：** 检索决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `limit` (integer, 可选): 要返回的最大决策数量（最新的优先）（默认：null）
    *   `tags_filter_include_all` (array of strings, 可选): 过滤器：项目必须包含所有这些标签。（默认：null）
    *   `tags_filter_include_any` (array of strings, 可选): 过滤器：项目必须包含至少一个这些标签。（默认：null）
*   **Pydantic 模型：** `GetDecisionsArgs`

#### 3.3.3 `search_decisions_fts`
*   **描述：** 使用 FTS 搜索决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `query_term` (string): 在决策中搜索的术语。（必需：是）
    *   `limit` (integer, 可选): 要返回的最大搜索结果数量。（默认：10）
*   **Pydantic 模型：** `SearchDecisionsArgs`

#### 3.3.4 `delete_decision_by_id`
*   **描述：** 通过 ID 删除决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `decision_id` (integer): 要删除的决策的 ID。（必需：是）
    *   **Pydantic 模型：** `DeleteDecisionByIdArgs`

### 3.4 进度跟踪工具

#### 3.4.1 `log_progress`
*   **描述：** 记录进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `status` (string): 当前状态（例如，'TODO'、'IN_PROGRESS'、'DONE'）（必需：是）
    *   `description` (string): 进度或任务的描述（必需：是）
    *   `parent_id` (integer, 可选): 父任务的 ID，如果这是子任务（默认：null）
    *   `linked_item_type` (string, 可选): 可选：此进度条目链接到的 ConPort 项的类型（例如，'decision'、'system_pattern'）（默认：null）
    *   `linked_item_id` (string, 可选): 可选：此进度条目链接到的 ConPort 项的 ID/键（需要 linked_item_type）（默认：null）
    *   `link_relationship_type` (string, 可选): 如果提供了 `linked_item_type` 和 `linked_item_id`，自动链接的关系类型（例如，"tracks_decision"）。（默认："relates_to_progress"）
*   **Pydantic 模型：** `LogProgressArgs`

#### 3.4.2 `get_progress`
*   **描述：** 检索进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `status_filter` (string, 可选): 按状态过滤条目（默认：null）
    *   `parent_id_filter` (integer, 可选): 按父任务 ID 过滤条目（默认：null）
    *   `limit` (integer, 可选): 要返回的最大条目数量（最新的优先）（默认：null）
*   **Pydantic 模型：** `GetProgressArgs`

#### 3.4.3 `update_progress`
*   **描述：** 更新现有进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `progress_id` (integer): 要更新的进度条目的 ID。（必需：是）
    *   `status` (string, 可选): 新状态（例如，'TODO'、'IN_PROGRESS'、'DONE'）（默认：null）
    *   `description` (string, 可选): 进度或任务的新描述（默认：null）
    *   `parent_id` (integer, 可选): 父任务的新 ID，如果要更改（默认：null）
*   **Pydantic 模型：** `UpdateProgressArgs`

#### 3.4.4 `delete_progress_by_id`
*   **描述：** 通过 ID 删除进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `progress_id` (integer): 要删除的进度条目的 ID。（必需：是）
*   **Pydantic 模型：** `DeleteProgressByIdArgs`

### 3.5 系统模式工具

#### 3.5.1 `log_system_pattern`
*   **描述：** 记录系统模式的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `name` (string): 系统模式的唯一名称（必需：是）
    *   `description` (string, 可选): 模式的描述（默认：null）
    *   `tags` (array of strings, 可选): 用于分类的可选标签（默认：null）
*   **Pydantic 模型：** `LogSystemPatternArgs`

#### 3.5.2 `get_system_patterns`
*   **描述：** 检索系统模式的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `tags_filter_include_all` (array of strings, 可选): 过滤器：项目必须包含所有这些标签。（默认：null）
    *   `tags_filter_include_any` (array of strings, 可选): 过滤器：项目必须包含至少一个这些标签。（默认：null）
*   **Pydantic 模型：** `GetSystemPatternsArgs`

#### 3.5.3 `delete_system_pattern_by_id`
*   **描述：** 通过 ID 删除系统模式的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `pattern_id` (integer): 要删除的系统模式的 ID。（必需：是）
*   **Pydantic 模型：** `DeleteSystemPatternByIdArgs`

### 3.6 自定义数据工具

#### 3.6.1 `log_custom_data`
*   **描述：** 记录自定义数据的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `category` (string): 自定义数据的类别（必需：是）
    *   `key` (string): 自定义数据的键（在类别内唯一）（必需：是）
    *   `value` (any): 自定义数据值（JSON 可序列化）（必需：是）
*   **Pydantic 模型：** `LogCustomDataArgs`

#### 3.6.2 `get_custom_data`
*   **描述：** 检索自定义数据的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `category` (string, 可选): 按类别过滤（默认：null）
    *   `key` (string, 可选): 按键过滤（需要类别）（默认：null）
*   **Pydantic 模型：** `GetCustomDataArgs`

#### 3.6.3 `delete_custom_data`
*   **描述：** 删除自定义数据的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `category` (string): 要删除的数据的类别（必需：是）
    *   `key` (string): 要删除的数据的键（必需：是）
*   **Pydantic 模型：** `DeleteCustomDataArgs`

#### 3.6.4 `search_custom_data_value_fts`
*   **描述：** 使用 FTS 搜索自定义数据值的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `query_term` (string): 在自定义数据（类别、键或值）中搜索的术语。（必需：是）
    *   `category_filter` (string, 可选): 可选：FTS 后将结果过滤到此类别。（默认：null）
    *   `limit` (integer, 可选): 要返回的最大搜索结果数量。（默认：10）
*   **Pydantic 模型：** `SearchCustomDataValueArgs`

#### 3.6.5 `search_project_glossary_fts`
*   **描述：** 使用 FTS 搜索项目术语表的参数。（注意：ProjectGlossary 是 custom_data 中的特定类别）。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `query_term` (string): 在术语表中搜索的术语。（必需：是）
    *   `limit` (integer, 可选): 要返回的最大搜索结果数量。（默认：10）
*   **Pydantic 模型：** `SearchProjectGlossaryArgs`

### 3.7 导入/导出工具

#### 3.7.1 `export_conport_to_markdown`
*   **描述：** 将 ConPort 数据导出到 markdown 文件的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `output_path` (string, 可选): 相对于 workspace_id 的可选输出目录路径。（默认：'./conport_export/'）
*   **Pydantic 模型：** `ExportConportToMarkdownArgs`

#### 3.7.2 `import_markdown_to_conport`
*   **描述：** 将 markdown 文件导入 ConPort 数据的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `input_path` (string, 可选): 相对于 workspace_id 的包含 markdown 文件的可选输入目录路径。（默认：'./conport_export/'）
*   **Pydantic 模型：** `ImportMarkdownToConportArgs`

### 3.8 知识图谱/链接工具

#### 3.8.1 `link_conport_items`
*   **描述：** 在两个 ConPort 项之间创建显式、类型化链接的参数，从而丰富**项目知识图谱**。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `source_item_type` (string): 源项的类型（必需：是）
    *   `source_item_id` (string): 源项的 ID 或键（必需：是）
    *   `target_item_type` (string): 目标项的类型（必需：是）
    *   `target_item_id` (string): 目标项的 ID 或键（必需：是）
    *   `relationship_type` (string): 链接的性质（必需：是）
    *   `description` (string, 可选): 链接的可选描述（默认：null）
*   **Pydantic 模型：** `LinkConportItemsArgs`

#### 3.8.2 `get_linked_items`
*   **描述：** 检索连接到特定 ConPort 项的链接的参数，允许遍历**项目知识图谱**。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `item_type` (string): 要查找链接的项的类型（例如，'decision'）（必需：是）
    *   `item_id` (string): 要查找链接的项的 ID 或键（必需：是）
    *   `relationship_type_filter` (string, 可选): 可选：按关系类型过滤（默认：null）
    *   `linked_item_type_filter` (string, 可选): 可选：按链接项的类型过滤（默认：null）
    *   `limit` (integer, 可选): 要返回的最大链接数量（默认：null）
*   **Pydantic 模型：** `GetLinkedItemsArgs`

### 3.9 批量操作

#### 3.9.1 `batch_log_items`
*   **描述：** 批量记录相同类型的多个项目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `item_type` (string): 要记录的项目类型（例如，'decision'、'progress_entry'、'system_pattern'、'custom_data'）（必需：是）
    *   `items` (array of objects): 字典列表，每个字典代表单个项目记录的参数。（必需：是）
*   **Pydantic 模型：** `BatchLogItemsArgs`

### 3.10 历史和元工具

#### 3.10.1 `get_item_history`
*   **描述：** 检索上下文项历史的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `item_type` (string): 项的类型：'product_context' 或 'active_context'（必需：是）
    *   `limit` (integer, 可选): 要返回的最大历史条目数量（最新的优先）（默认：null）
    *   `before_timestamp` (string, format: date-time, 可选): 返回此时间戳之前的条目（默认：null）
    *   `after_timestamp` (string, format: date-time, 可选): 返回此时间戳之后的条目（默认：null）
    *   `version` (integer, 可选): 返回特定版本（默认：null）
*   **Pydantic 模型：** `GetItemHistoryArgs`

#### 3.10.2 `get_conport_schema`
*   **描述：** 检索 ConPort 工具架构的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   **Pydantic 模型：** `GetConportSchemaArgs`

#### 3.10.3 `get_recent_activity_summary`
*   **描述：** 检索最近 ConPort 活动摘要的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `hours_ago` (integer, 可选): 回顾这么多小时的最近活动。与 'since_timestamp' 互斥。（默认：null）
    *   `since_timestamp` (string, format: date-time, 可选): 回顾自此特定时间戳以来的活动。与 'hours_ago' 互斥。（默认：null）
    *   `limit_per_type` (integer, 可选): 每种活动类型显示的最大最近项目数量（例如，5 个最近的决策）。（默认：5）
    *   **Pydantic 模型：** `GetRecentActivitySummaryArgs`

### 3.9 批量操作

#### 3.9.1 `batch_log_items`
*   **描述：** 批量记录相同类型的多个项目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `item_type` (string): 要记录的项目类型（例如，'decision'、'progress_entry'、'system_pattern'、'custom_data'）（必需：是）
    *   `items` (array of objects): 字典列表，每个字典代表单个项目记录的参数。（必需：是）
*   **Pydantic 模型：** `BatchLogItemsArgs`

### 3.10 历史和元工具

#### 3.10.1 `get_item_history`
*   **描述：** 检索上下文项历史的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `item_type` (string): 项的类型：'product_context' 或 'active_context'（必需：是）
    *   `limit` (integer, 可选): 要返回的最大历史条目数量（最新的优先）（默认：null）
    *   `before_timestamp` (string, format: date-time, 可选): 返回此时间戳之前的条目（默认：null）
    *   `after_timestamp` (string, format: date-time, 可选): 返回此时间戳之后的条目（默认：null）
    *   `version` (integer, 可选): 返回特定版本（默认：null）
*   **Pydantic 模型：** `GetItemHistoryArgs`

#### 3.10.2 `get_conport_schema`
*   **描述：** 检索 ConPort 工具架构的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   **Pydantic 模型：** `GetConportSchemaArgs`

#### 3.10.3 `get_recent_activity_summary`
*   **描述：** 检索最近 ConPort 活动摘要的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `hours_ago` (integer, 可选): 回顾这么多小时的最近活动。与 'since_timestamp' 互斥。（默认：null）
    *   `since_timestamp` (string, format: date-time, 可选): 回顾自此特定时间戳以来的活动。与 'hours_ago' 互斥。（默认：null）
    *   `limit_per_type` (integer, 可选): 每种活动类型显示的最大最近项目数量（例如，5 个最近的决策）。（默认：5）
    *   **Pydantic 模型：** `GetRecentActivitySummaryArgs`

## 4. 实现细节和指南

### 4.1 项目结构
服务器代码主要位于 `src/context_portal_mcp/` 目录中：
*   `main.py`: 服务器的入口点。处理 CLI 参数解析，设置 FastAPI 和 FastMCP 实例，注册 MCP 工具，并定义服务器生命周期。
*   `handlers/mcp_handlers.py`: 包含每个 MCP 工具的核心逻辑。这些函数由 `main.py` 中的工具包装器调用。
*   `db/database.py`: 管理与 SQLite 数据库的所有交互，包括连接处理、架构初始化和所有数据实体的 CRUD 操作。
*   `db/models.py`: 定义 Pydantic 模型用于：
    *   表示存储在数据库表中的数据结构（例如，`Decision`、`ProductContext`）。
    *   验证和构建传递给 MCP 工具的参数（例如，`LogDecisionArgs`、`UpdateContextArgs`）。
*   `core/config.py`: 处理配置方面，特别是基于 `workspace_id` 确定数据库路径。
*   `core/exceptions.py`: 定义在整个服务器中使用的自定义异常类（例如，`DatabaseError`、`ToolArgumentError`）。

### 4.2 运行服务器

服务器可以在两种主要模式下运行：

#### STDIO 模式
此模式适用于本地 MCP 客户端（如 IDE 扩展）的直接使用，`FastMCP` 库处理 STDIO 通信传输。
    *   命令（从项目根目录）：
        ```bash
        python src/context_portal_mcp/main.py --mode stdio --workspace_id "/path/to/your/workspace"
        ```
    *   或使用 CLI 入口点：
        ```bash
        conport --mode stdio --workspace_id "/path/to/your/workspace"
        ```
    *   **STDIO 模式中的 `workspace_id` 注意事项：** 服务器包含逻辑来检测是否字面传递了 `${workspaceFolder}`（即，未被客户端展开），在这种情况下将回退到使用当前工作目录作为 `workspace_id`，并发出警告。

#### HTTP 模式
服务器也可以使用 Uvicorn 作为 HTTP 服务运行：
    ```bash
    uvicorn src.context_portal_mcp.main:app --host 127.0.0.1 --port 8000
    ```
或使用 CLI 入口点：
    ```bash
    conport --mode http --host 127.0.0.1 --port 8000
    ```
在此模式下，MCP 端点通常在 `/mcp` 可用。

### 4.3 依赖项
关键的 Python 依赖项通过 `requirements.txt` 管理（或如果使用 Poetry/PDM，则在 `pyproject.toml` 中）。这些包括：
*   `fastapi`: 用于 Web 服务器框架。
*   `uvicorn[standard]`: 作为 ASGI 服务器在 HTTP 模式下运行 FastAPI。
*   `pydantic`: 用于数据验证和设置管理。
*   `mcp[cli]`（或 `mcp.py`）: 模型上下文协议 SDK，特别是用于服务器实现的 `FastMCP`。
*   `sqlite3` 是 Python 标准库的一部分。

### 4.4 数据库位置
每个工作空间的 SQLite 数据库文件通常创建在：
`<workspace_id>/context_portal/context.db`
此路径由 `core/config.py` 中的 `get_database_path` 函数确定。

### 4.5 添加新工具
要添加新的 MCP 工具：
1.  **定义参数模型：** 在 `db/models.py` 中创建一个继承自 `BaseArgs` 的新 Pydantic 模型（或如果不需要 `workspace_id`，则直接从 `BaseModel` 继承，尽管通常需要）。此模型定义新工具的预期参数。
2.  **实现处理程序逻辑：** 在 `handlers/mcp_handlers.py` 中添加新的处理程序函数。此函数将验证的 Pydantic 参数模型作为输入并执行核心逻辑，可能与 `db/database.py` 交互。
3.  **在 `main.py` 中注册工具：**
    *   导入新的参数模型和处理程序函数。
    *   在新的异步包装器函数上使用 `@conport_mcp.tool(name="your_new_tool_name")` 装饰器。
    *   此包装器函数将：
        *   接收 `raw_args_from_fastmcp: Dict[str, Any]` 和 `ctx: MCPContext`。
        *   从 `raw_args_from_fastmcp` 中提取必要字段。
        *   使用这些字段实例化您的 Pydantic 参数模型。
        *   使用 Pydantic 模型实例调用 `mcp_handlers.py` 中的处理程序函数。
        *   返回处理程序的结果。
4.  **更新 `TOOL_ARG_MODELS`：** 将您的新工具名称及其参数模型添加到 `db/models.py` 中的 `TOOL_ARG_MODELS` 字典（由 `get_conport_schema` 使用）。
5.  **数据库架构（如果需要）：** 如果新工具需要更改数据库架构（新表、列），请相应更新 `db/database.py` 的 `initialize_database` 函数，包括任何必要的 `ALTER TABLE` 语句进行迁移。

## 3. MCP 工具参考

ConPort 服务器公开以下 MCP 工具。这些工具允许 AI 代理与**项目知识图谱**交互并在其基础上构建，并检索对**检索增强生成 (RAG)** 至关重要的特定信息。所有工具都需要一个 `workspace_id` 参数，这是工作空间的标识符（例如，绝对路径），以确保数据操作在正确的上下文中执行。

### 3.1 产品上下文工具

#### 3.1.1 `get_product_context`
*   **描述：** 获取产品或活动上下文的参数（仅需要 workspace_id）。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
*   **Pydantic 模型：** `GetContextArgs`

#### 3.1.2 `update_product_context`
*   **描述：** 更新产品或活动上下文的参数。提供 'content' 进行完整更新或 'patch_content' 进行部分更新。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `content` (object, 可选): 作为字典的完整新上下文内容。覆盖现有内容。（默认：null）
    *   `patch_content` (object, 可选): 要应用的更改字典（添加/更新键）。使用值 `\"__DELETE__\"` 删除键。（默认：null）
*   **Pydantic 模型：** `UpdateContextArgs`

### 3.2 活动上下文工具

#### 3.2.1 `get_active_context`
*   **描述：** 获取产品或活动上下文的参数（仅需要 workspace_id）。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
*   **Pydantic 模型：** `GetContextArgs`

#### 3.2.2 `update_active_context`
*   **描述：** 更新产品或活动上下文的参数。提供 'content' 进行完整更新或 'patch_content' 进行部分更新。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `content` (object, 可选): 作为字典的完整新上下文内容。覆盖现有内容。（默认：null）
    *   `patch_content` (object, 可选): 要应用的更改字典（添加/更新键）。使用值 `\"__DELETE__\"` 删除键。（默认：null）
*   **Pydantic 模型：** `UpdateContextArgs`

### 3.3 决策记录工具

#### 3.3.1 `log_decision`
*   **描述：** 记录决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `summary` (string): 决策的简洁摘要（必需：是）
    *   `rationale` (string, 可选): 决策背后的推理（默认：null）
    *   `implementation_details` (string, 可选): 关于决策将如何/已经实现的详细信息（默认：null）
    *   `tags` (array of strings, 可选): 用于分类的可选标签（默认：null）
*   **Pydantic 模型：** `LogDecisionArgs`

#### 3.3.2 `get_decisions`
*   **描述：** 检索决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `limit` (integer, 可选): 要返回的最大决策数量（最新的优先）（默认：null）
    *   `tags_filter_include_all` (array of strings, 可选): 过滤器：项目必须包含所有这些标签。（默认：null）
    *   `tags_filter_include_any` (array of strings, 可选): 过滤器：项目必须包含至少一个这些标签。（默认：null）
*   **Pydantic 模型：** `GetDecisionsArgs`

#### 3.3.3 `search_decisions_fts`
*   **描述：** 使用 FTS 搜索决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `query_term` (string): 在决策中搜索的术语。（必需：是）
    *   `limit` (integer, 可选): 要返回的最大搜索结果数量。（默认：10）
*   **Pydantic 模型：** `SearchDecisionsArgs`

#### 3.3.4 `delete_decision_by_id`
*   **描述：** 通过 ID 删除决策的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `decision_id` (integer): 要删除的决策的 ID。（必需：是）
    *   **Pydantic 模型：** `DeleteDecisionByIdArgs`

### 3.4 进度跟踪工具

#### 3.4.1 `log_progress`
*   **描述：** 记录进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `status` (string): 当前状态（例如，'TODO'、'IN_PROGRESS'、'DONE'）（必需：是）
    *   `description` (string): 进度或任务的描述（必需：是）
    *   `parent_id` (integer, 可选): 父任务的 ID，如果这是子任务（默认：null）
    *   `linked_item_type` (string, 可选): 可选：此进度条目链接到的 ConPort 项的类型（例如，'decision'、'system_pattern'）（默认：null）
    *   `linked_item_id` (string, 可选): 可选：此进度条目链接到的 ConPort 项的 ID/键（需要 linked_item_type）（默认：null）
    *   `link_relationship_type` (string, 可选): 如果提供了 `linked_item_type` 和 `linked_item_id`，自动链接的关系类型（例如，"tracks_decision"）。（默认："relates_to_progress"）
*   **Pydantic 模型：** `LogProgressArgs`

#### 3.4.2 `get_progress`
*   **描述：** 检索进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `status_filter` (string, 可选): 按状态过滤条目（默认：null）
    *   `parent_id_filter` (integer, 可选): 按父任务 ID 过滤条目（默认：null）
    *   `limit` (integer, 可选): 要返回的最大条目数量（最新的优先）（默认：null）
*   **Pydantic 模型：** `GetProgressArgs`

#### 3.4.3 `update_progress`
*   **描述：** 更新现有进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `progress_id` (integer): 要更新的进度条目的 ID。（必需：是）
    *   `status` (string, 可选): 新状态（例如，'TODO'、'IN_PROGRESS'、'DONE'）（默认：null）
    *   `description` (string, 可选): 进度或任务的新描述（默认：null）
    *   `parent_id` (integer, 可选): 父任务的新 ID，如果要更改（默认：null）
*   **Pydantic 模型：** `UpdateProgressArgs`

#### 3.4.4 `delete_progress_by_id`
*   **描述：** 通过 ID 删除进度条目的参数。
*   **参数：**
    *   `workspace_id` (string): 工作空间的标识符（例如，绝对路径）（必需：是）
    *   `progress_id` (integer): 要删除的进度条目的 ID。（必需：是）
*   **Pydantic 模型：** `DeleteProgressByIdArgs`
